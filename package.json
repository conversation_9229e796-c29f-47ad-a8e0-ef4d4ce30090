{"name": "menli-app", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~53.0.22", "expo-router": "~5.1.5", "expo-status-bar": "~2.2.3", "nativewind": "^2.0.11", "react": "19.0.0", "react-native": "0.79.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "tailwindcss": "^3.3.2", "expo-constants": "~17.1.7", "expo-linking": "~7.1.7"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}